# Riva Gleam - Makefile
# A comprehensive build system for Gleam projects with Lustre frontend and Wisp API

.PHONY: help install build build-js build-erl test test-watch clean dev dev-frontend dev-api format check lint deps deps-update docs serve stop-serve status

# Default target
.DEFAULT_GOAL := help

# Configuration
PORT ?= 3001
API_PORT ?= 8080
GLEAM_TARGET ?= javascript

# Colors for output
BLUE := \033[34m
GREEN := \033[32m
YELLOW := \033[33m
RED := \033[31m
RESET := \033[0m

## Help
help: ## Show this help message
	@echo "$(BLUE)Riva Gleam - Development Commands$(RESET)"
	@echo "=================================="
	@echo ""
	@echo "$(GREEN)Quick Start:$(RESET)"
	@echo "  make install    # Install dependencies"
	@echo "  make dev        # Start development server"
	@echo "  make test       # Run tests"
	@echo ""
	@echo "$(GREEN)Available commands:$(RESET)"
	@awk 'BEGIN {FS = ":.*##"} /^[a-zA-Z_-]+:.*##/ { printf "  $(BLUE)%-15s$(RESET) %s\n", $$1, $$2 }' $(MAKEFILE_LIST)

## Installation & Dependencies
install: deps ## Install all dependencies
	@echo "$(GREEN)✅ Installation complete!$(RESET)"

deps: ## Download and install Gleam dependencies
	@echo "$(BLUE)📦 Installing Gleam dependencies...$(RESET)"
	gleam deps download

deps-update: ## Update all dependencies to latest versions
	@echo "$(BLUE)🔄 Updating dependencies...$(RESET)"
	gleam deps update

## Building
build: build-js ## Build the project (default: JavaScript target)

build-js: ## Build for JavaScript target
	@echo "$(BLUE)🔨 Building for JavaScript target...$(RESET)"
	gleam build --target javascript
	@echo "$(GREEN)✅ JavaScript build complete!$(RESET)"

build-erl: ## Build for Erlang target
	@echo "$(BLUE)🔨 Building for Erlang target...$(RESET)"
	gleam build --target erlang
	@echo "$(GREEN)✅ Erlang build complete!$(RESET)"

build-all: build-js build-erl ## Build for both JavaScript and Erlang targets

## Testing
test: ## Run all tests
	@echo "$(BLUE)🧪 Running tests...$(RESET)"
	gleam test

test-watch: ## Run tests in watch mode (requires entr)
	@echo "$(BLUE)👀 Running tests in watch mode...$(RESET)"
	@echo "$(YELLOW)Press Ctrl+C to stop$(RESET)"
	find src test -name "*.gleam" | entr -c gleam test

test-coverage: ## Run tests with coverage (if available)
	@echo "$(BLUE)📊 Running tests with coverage...$(RESET)"
	gleam test

## Development
dev: build-js ## Start the full development environment
	@echo "$(GREEN)🚀 Starting Riva Gleam Development Environment$(RESET)"
	@echo "=============================================="
	@echo ""
	@echo "$(BLUE)📋 Development server features:$(RESET)"
	@echo "- ✅ Serves frontend at http://localhost:$(PORT)"
	@echo "- ✅ Avoids CORS issues with local files"
	@echo "- ✅ Counter with Increment/Decrement/Reset buttons"
	@echo "- ✅ Loading states for API calls"
	@echo "- ✅ Mock API integration with effects"
	@echo ""
	@echo "$(GREEN)🌐 Starting HTTP server on port $(PORT)...$(RESET)"
	@echo "$(YELLOW)Press Ctrl+C to stop the server$(RESET)"
	python3 serve.py

dev-frontend: build-js ## Start only the frontend development server
	@echo "$(BLUE)🎨 Starting frontend development server...$(RESET)"
	python3 serve.py

dev-api: build-erl ## Start only the API server (Erlang target)
	@echo "$(BLUE)🔌 Starting API server...$(RESET)"
	gleam run --target erlang

## Code Quality
format: ## Format all Gleam code
	@echo "$(BLUE)✨ Formatting code...$(RESET)"
	gleam format

format-check: ## Check if code is properly formatted
	@echo "$(BLUE)🔍 Checking code formatting...$(RESET)"
	gleam format --check src test

check: format-check test ## Run all code quality checks

lint: format-check ## Alias for format-check (Gleam doesn't have a separate linter)

## Documentation
docs: ## Generate documentation
	@echo "$(BLUE)📚 Generating documentation...$(RESET)"
	gleam docs build

docs-open: docs ## Generate and open documentation
	@echo "$(BLUE)🌐 Opening documentation...$(RESET)"
	@if command -v xdg-open > /dev/null; then \
		xdg-open build/docs/index.html; \
	elif command -v open > /dev/null; then \
		open build/docs/index.html; \
	else \
		echo "$(YELLOW)⚠️  Please open build/docs/index.html manually$(RESET)"; \
	fi

## Utilities
serve: dev ## Alias for dev command

stop-serve: ## Stop any running development servers
	@echo "$(BLUE)🛑 Stopping development servers...$(RESET)"
	@pkill -f "python3 serve.py" || echo "$(YELLOW)No Python server running$(RESET)"
	@pkill -f "gleam run" || echo "$(YELLOW)No Gleam server running$(RESET)"

clean: ## Clean build artifacts
	@echo "$(BLUE)🧹 Cleaning build artifacts...$(RESET)"
	rm -rf build/
	@echo "$(GREEN)✅ Clean complete!$(RESET)"

clean-deps: ## Clean dependencies (forces re-download)
	@echo "$(BLUE)🧹 Cleaning dependencies...$(RESET)"
	rm -rf build/packages/
	gleam deps download

status: ## Show project status and running processes
	@echo "$(BLUE)📊 Riva Gleam Project Status$(RESET)"
	@echo "============================="
	@echo ""
	@echo "$(GREEN)Project Info:$(RESET)"
	@echo "  Name: $(shell grep '^name' gleam.toml | cut -d'"' -f2)"
	@echo "  Version: $(shell grep '^version' gleam.toml | cut -d'"' -f2)"
	@echo ""
	@echo "$(GREEN)Build Status:$(RESET)"
	@if [ -d "build/dev/javascript" ]; then \
		echo "  JavaScript: $(GREEN)✅ Built$(RESET)"; \
	else \
		echo "  JavaScript: $(RED)❌ Not built$(RESET)"; \
	fi
	@if [ -d "build/dev/erlang" ]; then \
		echo "  Erlang: $(GREEN)✅ Built$(RESET)"; \
	else \
		echo "  Erlang: $(RED)❌ Not built$(RESET)"; \
	fi
	@echo ""
	@echo "$(GREEN)Running Processes:$(RESET)"
	@if pgrep -f "python3 serve.py" > /dev/null; then \
		echo "  Frontend Server: $(GREEN)✅ Running on port $(PORT)$(RESET)"; \
	else \
		echo "  Frontend Server: $(RED)❌ Not running$(RESET)"; \
	fi
	@if pgrep -f "gleam run" > /dev/null; then \
		echo "  API Server: $(GREEN)✅ Running$(RESET)"; \
	else \
		echo "  API Server: $(RED)❌ Not running$(RESET)"; \
	fi

## Advanced
watch: ## Watch for file changes and rebuild (requires entr)
	@echo "$(BLUE)👀 Watching for changes...$(RESET)"
	@echo "$(YELLOW)Press Ctrl+C to stop$(RESET)"
	find src -name "*.gleam" | entr -c make build-js

ci: deps format-check test ## Run CI pipeline (install, format check, test)
	@echo "$(GREEN)✅ CI pipeline completed successfully!$(RESET)"

release: clean ci build-all docs ## Prepare a release build
	@echo "$(GREEN)🎉 Release build completed!$(RESET)"

# Development shortcuts
js: build-js ## Shortcut for build-js
erl: build-erl ## Shortcut for build-erl
t: test ## Shortcut for test
f: format ## Shortcut for format
d: dev ## Shortcut for dev
