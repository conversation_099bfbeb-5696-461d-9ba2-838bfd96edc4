name = "riva_gleam"
version = "1.0.0"
description = "A modern web application built with Gleam, Lustre, and Wisp"
licences = ["Apache-2.0"]

# Project metadata
# repository = { type = "github", user = "your-username", repo = "riva_gleam" }
# links = [{ title = "Website", href = "https://your-domain.com" }]

# Workspace configuration
# This is now a workspace with separate client and server projects
# Use the individual gleam.toml files in client/ and server/ directories

# Default target for workspace operations
target = "javascript"

# Workspace dependencies (shared across client and server)
[dependencies]
gleam_stdlib = ">= 0.44.0 and < 2.0.0"

[dev-dependencies]
gleeunit = ">= 1.0.0 and < 2.0.0"
