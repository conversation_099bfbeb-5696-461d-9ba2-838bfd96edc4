#!/bin/bash

# Riva Gleam Development Startup Script
# This script builds the frontend and provides instructions for running the full stack

echo "🚀 Starting Riva Gleam Development Environment"
echo "=============================================="

# Build the frontend
echo "📦 Building frontend (JavaScript target)..."
gleam build --target javascript

if [ $? -eq 0 ]; then
    echo "✅ Frontend built successfully!"
    echo ""
    echo "🚀 Starting development server..."
    echo ""
    echo "📋 Development server features:"
    echo "- ✅ Serves frontend at http://localhost:3000"
    echo "- ✅ Avoids CORS issues with local files"
    echo "- ✅ Counter with Increment/Decrement/Reset buttons"
    echo "- ✅ Loading states for API calls"
    echo "- ✅ Mock API integration with effects"
    echo "- ⏳ Real HTTP API integration (next step)"
    echo ""
    echo "🌐 Starting HTTP server on port 3000..."
    python3 serve.py
else
    echo "❌ Frontend build failed!"
    exit 1
fi
