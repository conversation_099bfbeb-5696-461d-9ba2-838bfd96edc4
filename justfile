# Riva Gleam - Justfile
# A modern task runner for Gleam projects with <PERSON><PERSON>re frontend and Wisp API
# Now supports separate client (JavaScript) and server (Erlang) builds

# Configuration
port := "3001"
api_port := "8080"
client_target := "javascript"
server_target := "erlang"

# Default recipe
default: help

# Show available recipes
help:
    @echo "🚀 Riva Gleam - Development Commands"
    @echo "===================================="
    @echo ""
    @echo "Quick Start:"
    @echo "  just install    # Install dependencies for both client and server"
    @echo "  just build      # Build both client and server"
    @echo "  just dev        # Start development servers"
    @echo "  just test       # Run tests"
    @echo ""
    @echo "Available commands:"
    @just --list

# Install all dependencies for both client and server
install: install-client install-server
    @echo "✅ Installation complete for both client and server!"

# Install client dependencies
install-client:
    @echo "📦 Installing client dependencies..."
    cd client && gleam deps download

# Install server dependencies
install-server:
    @echo "📦 Installing server dependencies..."
    cd server && gleam deps download

# Update all dependencies
deps-update: update-client update-server
    @echo "🔄 Updated dependencies for both client and server!"

# Update client dependencies
update-client:
    @echo "🔄 Updating client dependencies..."
    cd client && gleam deps update

# Update server dependencies
update-server:
    @echo "🔄 Updating server dependencies..."
    cd server && gleam deps update

# Build both client and server (default)
build: build-client build-server

# Build client for JavaScript target
build-client:
    @echo "🔨 Building client for JavaScript target..."
    cd client && gleam build --target {{client_target}}
    @echo "✅ Client build complete!"

# Build server for Erlang target
build-server:
    @echo "🔨 Building server for Erlang target..."
    cd server && gleam build --target {{server_target}}
    @echo "✅ Server build complete!"

# Legacy aliases for backward compatibility
build-js: build-client
build-erl: build-server
# Legacy build commands (now removed, use build-client/build-server)

# Run all tests for both client and server
test: test-client test-server
    @echo "✅ All tests completed!"

# Run client tests
test-client:
    @echo "🧪 Running client tests..."
    cd client && gleam test

# Run server tests
test-server:
    @echo "🧪 Running server tests..."
    cd server && gleam test

# Run tests in watch mode (requires entr)
test-watch:
    @echo "👀 Running tests in watch mode..."
    @echo "Press Ctrl+C to stop"
    find client server -name "*.gleam" | entr -c just test

# Start the full development environment
dev: build
    @echo "🚀 Starting Riva Gleam Development Environment"
    @echo "=============================================="
    @echo ""
    @echo "📋 Development server features:"
    @echo "- ✅ Serves frontend at http://localhost:{{port}}"
    @echo "- ✅ Avoids CORS issues with local files"
    @echo "- ✅ Counter with Increment/Decrement/Reset buttons"
    @echo "- ✅ Loading states for API calls"
    @echo "- ✅ Mock API integration with effects"
    @echo ""
    @echo "🌐 Starting HTTP server on port {{port}}..."
    @echo "Press Ctrl+C to stop the server"
    python3 serve.py

# Start only the frontend development server
dev-frontend: build-client
    @echo "🎨 Starting frontend development server..."
    python3 serve.py

# Start only the API server
dev-api: build-server
    @echo "🔌 Starting API server..."
    cd server && gleam run --target {{server_target}}

# Format all Gleam code in both client and server
format: format-client format-server
    @echo "✅ All code formatted!"

# Format client code
format-client:
    @echo "✨ Formatting client code..."
    cd client && gleam format

# Format server code
format-server:
    @echo "✨ Formatting server code..."
    cd server && gleam format

# Check if code is properly formatted
format-check: format-check-client format-check-server
    @echo "✅ All code formatting checked!"

# Check client code formatting
format-check-client:
    @echo "🔍 Checking client code formatting..."
    cd client && gleam format --check

# Check server code formatting
format-check-server:
    @echo "🔍 Checking server code formatting..."
    cd server && gleam format --check

# Run all code quality checks
check: format-check test

# Generate documentation for both client and server
docs: docs-client docs-server
    @echo "✅ All documentation generated!"

# Generate client documentation
docs-client:
    @echo "📚 Generating client documentation..."
    cd client && gleam docs build

# Generate server documentation
docs-server:
    @echo "📚 Generating server documentation..."
    cd server && gleam docs build

# Stop any running development servers
stop:
    @echo "🛑 Stopping development servers..."
    -pkill -f "python3 serve.py" || echo "No Python server running"
    -pkill -f "gleam run" || echo "No Gleam server running"

# Clean build artifacts for both client and server
clean: clean-client clean-server
    @echo "✅ All build artifacts cleaned!"

# Clean client build artifacts
clean-client:
    @echo "🧹 Cleaning client build artifacts..."
    cd client && rm -rf build/

# Clean server build artifacts
clean-server:
    @echo "🧹 Cleaning server build artifacts..."
    cd server && rm -rf build/

# Clean dependencies (forces re-download)
clean-deps: clean-deps-client clean-deps-server
    @echo "✅ All dependencies cleaned!"

# Clean client dependencies
clean-deps-client:
    @echo "🧹 Cleaning client dependencies..."
    cd client && rm -rf build/packages/ && gleam deps download

# Clean server dependencies
clean-deps-server:
    @echo "🧹 Cleaning server dependencies..."
    cd server && rm -rf build/packages/ && gleam deps download

# Show project status
status:
    @echo "📊 Riva Gleam Project Status"
    @echo "============================="
    @echo ""
    @echo "Project Info:"
    @echo "  Name: $(grep '^name' gleam.toml | cut -d'\"' -f2)"
    @echo "  Version: $(grep '^version' gleam.toml | cut -d'\"' -f2)"
    @echo ""
    @echo "Build Status:"
    #!/usr/bin/env bash
    if [ -d "build/dev/javascript" ]; then
        echo "  JavaScript: ✅ Built"
    else
        echo "  JavaScript: ❌ Not built"
    fi
    if [ -d "build/dev/erlang" ]; then
        echo "  Erlang: ✅ Built"
    else
        echo "  Erlang: ❌ Not built"
    fi
    echo ""
    echo "Running Processes:"
    if pgrep -f "python3 serve.py" > /dev/null; then
        echo "  Frontend Server: ✅ Running on port {{port}}"
    else
        echo "  Frontend Server: ❌ Not running"
    fi
    if pgrep -f "gleam run" > /dev/null; then
        echo "  API Server: ✅ Running"
    else
        echo "  API Server: ❌ Not running"
    fi

# Watch for file changes and rebuild (requires entr)
watch:
    @echo "👀 Watching for changes..."
    @echo "Press Ctrl+C to stop"
    find src -name "*.gleam" | entr -c just build-js

# Run CI pipeline
ci: deps format-check test
    @echo "✅ CI pipeline completed successfully!"

# Prepare a release build
release: clean ci build-all docs
    @echo "🎉 Release build completed!"

# Development shortcuts
alias js := build-js
alias erl := build-erl
alias t := test
alias f := format
alias d := dev
alias s := status
