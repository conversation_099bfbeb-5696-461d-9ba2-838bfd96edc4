import gleam/dynamic/decode
import sqlight

// TYPES -----------------------------------------------------------------------

/// Database connection wrapper
pub opaque type Database {
  Database(connection: sqlight.Connection)
}

/// Database error types with proper constructors
pub type DatabaseError {
  ConnectionError(String)
  QueryError(String)
  InitializationError(String)
}

// DATABASE INITIALIZATION -----------------------------------------------------

/// Initialize database connection and create tables if they don't exist
pub fn init() -> Result(Database, DatabaseError) {
  case sqlight.open(":memory:") {
    Ok(conn) -> {
      case create_tables(conn) {
        Ok(_) -> Ok(Database(conn))
        Error(err) -> {
          let _ = sqlight.close(conn)
          Error(InitializationError("Failed to create tables: " <> err))
        }
      }
    }
    Error(sqlight.SqlightError(_, message, _)) -> Error(ConnectionError("Failed to open database: " <> message))
  }
}

/// Create the counter table if it doesn't exist
fn create_tables(conn: sqlight.Connection) -> Result(Nil, String) {
  let create_table_sql = "
    CREATE TABLE IF NOT EXISTS counter (
      id INTEGER PRIMARY KEY CHECK (id = 1),
      value INTEGER NOT NULL DEFAULT 0
    );
    INSERT OR IGNORE INTO counter (id, value) VALUES (1, 0);
  "

  case sqlight.exec(create_table_sql, on: conn) {
    Ok(_) -> Ok(Nil)
    Error(sqlight.SqlightError(_, message, _)) -> Error(message)
  }
}

// COUNTER OPERATIONS ----------------------------------------------------------

/// Get the current counter value
pub fn get_counter(db: Database) -> Result(Int, DatabaseError) {
  let Database(conn) = db
  let query = "SELECT value FROM counter WHERE id = 1"

  let decoder = {
    use value <- decode.field(0, decode.int)
    decode.success(value)
  }

  case sqlight.query(query, on: conn, with: [], expecting: decoder) {
    Ok([value]) -> Ok(value)
    Ok([]) -> Error(QueryError("Counter not found"))
    Ok(_) -> Error(QueryError("Multiple counter rows found"))
    Error(sqlight.SqlightError(_, message, _)) -> Error(QueryError("Failed to get counter: " <> message))
  }
}

/// Increment the counter and return the new value
pub fn increment_counter(db: Database) -> Result(Int, DatabaseError) {
  let Database(conn) = db
  let update_sql = "UPDATE counter SET value = value + 1 WHERE id = 1"

  case sqlight.exec(update_sql, on: conn) {
    Ok(_) -> get_counter(db)
    Error(sqlight.SqlightError(_, message, _)) -> Error(QueryError("Failed to increment counter: " <> message))
  }
}

/// Decrement the counter and return the new value
pub fn decrement_counter(db: Database) -> Result(Int, DatabaseError) {
  let Database(conn) = db
  let update_sql = "UPDATE counter SET value = value - 1 WHERE id = 1"

  case sqlight.exec(update_sql, on: conn) {
    Ok(_) -> get_counter(db)
    Error(sqlight.SqlightError(_, message, _)) -> Error(QueryError("Failed to decrement counter: " <> message))
  }
}

/// Reset the counter to 0 and return the new value
pub fn reset_counter(db: Database) -> Result(Int, DatabaseError) {
  let Database(conn) = db
  let update_sql = "UPDATE counter SET value = 0 WHERE id = 1"

  case sqlight.exec(update_sql, on: conn) {
    Ok(_) -> get_counter(db)
    Error(sqlight.SqlightError(_, message, _)) -> Error(QueryError("Failed to reset counter: " <> message))
  }
}

/// Close the database connection
pub fn close(db: Database) -> Result(Nil, DatabaseError) {
  let Database(conn) = db
  case sqlight.close(conn) {
    Ok(_) -> Ok(Nil)
    Error(sqlight.SqlightError(_, message, _)) -> Error(ConnectionError("Failed to close database: " <> message))
  }
}
