# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# All files
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true

# Gleam files
[*.gleam]
indent_style = space
indent_size = 2

# JavaScript/TypeScript files
[*.{js,mjs,ts}]
indent_style = space
indent_size = 2

# Python files
[*.py]
indent_style = space
indent_size = 4

# HTML files
[*.html]
indent_style = space
indent_size = 2

# CSS files
[*.css]
indent_style = space
indent_size = 2

# JSON files
[*.json]
indent_style = space
indent_size = 2

# YAML files
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# TOML files
[*.toml]
indent_style = space
indent_size = 2

# Markdown files
[*.md]
indent_style = space
indent_size = 2
trim_trailing_whitespace = false

# Makefile
[{Makefile,*.mk}]
indent_style = tab
indent_size = 4

# Shell scripts
[*.sh]
indent_style = space
indent_size = 4
