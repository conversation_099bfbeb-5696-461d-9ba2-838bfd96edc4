name: test

on:
  push:
    branches:
      - master
      - main
  pull_request:

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: erlef/setup-beam@v1
        with:
          otp-version: "27.1.2"
          gleam-version: "1.11.1"
          rebar3-version: "3"
          # elixir-version: "1"
      - run: gleam deps download
      - run: gleam test
      - run: gleam format --check src test
