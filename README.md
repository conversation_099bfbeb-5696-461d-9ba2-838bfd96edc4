# riva_gleam

[![Package Version](https://img.shields.io/hexpm/v/riva_gleam)](https://hex.pm/packages/riva_gleam)
[![Hex Docs](https://img.shields.io/badge/hex-docs-ffaff3)](https://hexdocs.pm/riva_gleam/)

```sh
gleam add riva_gleam@1
```
```gleam
import riva_gleam

pub fn main() -> Nil {
  // TODO: An example of the project in use
}
```

Further documentation can be found at <https://hexdocs.pm/riva_gleam>.

## Development

This project includes multiple build tools for your convenience:

### Quick Start

```sh
# Using Make (recommended)
make install    # Install dependencies
make dev        # Start development server
make test       # Run tests

# Using Just (modern alternative)
just install    # Install dependencies
just dev        # Start development server
just test       # Run tests

# Using the dev script
./dev.sh install    # Install dependencies
./dev.sh dev        # Start development server
./dev.sh test       # Run tests

# Using Gleam directly
gleam deps download  # Install dependencies
gleam build --target javascript  # Build for web
gleam test          # Run tests
```

### Available Commands

| Command | Make | Just | Script | Description |
|---------|------|------|--------|-------------|
| Install deps | `make install` | `just install` | `./dev.sh install` | Download Gleam dependencies |
| Build (JS) | `make build` | `just build` | `./dev.sh build` | Build for JavaScript target |
| Build (Erlang) | `make build-erl` | `just build-erl` | `./dev.sh build-erl` | Build for Erlang target |
| Run tests | `make test` | `just test` | `./dev.sh test` | Run all tests |
| Start dev server | `make dev` | `just dev` | `./dev.sh dev` | Start development server |
| Format code | `make format` | `just format` | `./dev.sh format` | Format Gleam code |
| Check format | `make check` | `just check` | `./dev.sh check` | Check code formatting |
| Generate docs | `make docs` | `just docs` | `./dev.sh docs` | Generate documentation |
| Clean build | `make clean` | `just clean` | `./dev.sh clean` | Remove build artifacts |
| Project status | `make status` | `just status` | `./dev.sh status` | Show project status |

### Development Server

The development server runs on **http://localhost:3001** and includes:
- ✅ Frontend served with proper MIME types
- ✅ CORS headers for local development
- ✅ Mock API endpoints for testing
- ✅ Hot reload support (manual refresh)

### Testing

```sh
# Run tests once
make test

# Run tests in watch mode (requires 'entr')
make test-watch
```
