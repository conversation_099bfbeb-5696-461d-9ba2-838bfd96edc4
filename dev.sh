#!/bin/bash

# Riva Gleam - Development Script
# A simple shell script for common development tasks

set -e  # Exit on any error

# Colors
BLUE='\033[34m'
GREEN='\033[32m'
YELLOW='\033[33m'
RED='\033[31m'
RESET='\033[0m'

# Configuration
PORT=${PORT:-3001}
API_PORT=${API_PORT:-8080}

# Helper functions
log_info() {
    echo -e "${BLUE}$1${RESET}"
}

log_success() {
    echo -e "${GREEN}$1${RESET}"
}

log_warning() {
    echo -e "${YELLOW}$1${RESET}"
}

log_error() {
    echo -e "${RED}$1${RESET}"
}

show_help() {
    echo -e "${BLUE}Riva Gleam - Development Script${RESET}"
    echo "================================"
    echo ""
    echo -e "${GREEN}Usage:${RESET} ./dev.sh <command>"
    echo ""
    echo -e "${GREEN}Commands:${RESET}"
    echo "  install       Install dependencies"
    echo "  build         Build for JavaScript (default)"
    echo "  build-js      Build for JavaScript target"
    echo "  build-erl     Build for Erlang target"
    echo "  test          Run tests"
    echo "  test-watch    Run tests in watch mode"
    echo "  dev           Start development server"
    echo "  format        Format code"
    echo "  check         Check code formatting"
    echo "  docs          Generate documentation"
    echo "  clean         Clean build artifacts"
    echo "  status        Show project status"
    echo "  stop          Stop running servers"
    echo ""
    echo -e "${GREEN}Examples:${RESET}"
    echo "  ./dev.sh install"
    echo "  ./dev.sh dev"
    echo "  ./dev.sh test"
}

install_deps() {
    log_info "📦 Installing Gleam dependencies..."
    gleam deps download
    log_success "✅ Dependencies installed!"
}

build_js() {
    log_info "🔨 Building for JavaScript target..."
    gleam build --target javascript
    log_success "✅ JavaScript build complete!"
}

build_erl() {
    log_info "🔨 Building for Erlang target..."
    gleam build --target erlang
    log_success "✅ Erlang build complete!"
}

run_tests() {
    log_info "🧪 Running tests..."
    gleam test
}

run_tests_watch() {
    log_info "👀 Running tests in watch mode..."
    log_warning "Press Ctrl+C to stop"
    if command -v entr > /dev/null; then
        find src test -name "*.gleam" | entr -c gleam test
    else
        log_error "❌ 'entr' command not found. Install it with: brew install entr (macOS) or apt install entr (Ubuntu)"
        exit 1
    fi
}

start_dev() {
    log_info "🚀 Starting Riva Gleam Development Environment"
    echo "=============================================="
    echo ""
    log_info "📋 Development server features:"
    echo "- ✅ Serves frontend at http://localhost:$PORT"
    echo "- ✅ Avoids CORS issues with local files"
    echo "- ✅ Counter with Increment/Decrement/Reset buttons"
    echo "- ✅ Loading states for API calls"
    echo "- ✅ Mock API integration with effects"
    echo ""
    
    # Build first
    build_js
    
    log_info "🌐 Starting HTTP server on port $PORT..."
    log_warning "Press Ctrl+C to stop the server"
    python3 serve.py
}

format_code() {
    log_info "✨ Formatting code..."
    gleam format
    log_success "✅ Code formatted!"
}

check_format() {
    log_info "🔍 Checking code formatting..."
    gleam format --check src test
    log_success "✅ Code formatting is correct!"
}

generate_docs() {
    log_info "📚 Generating documentation..."
    gleam docs build
    log_success "✅ Documentation generated!"
}

clean_build() {
    log_info "🧹 Cleaning build artifacts..."
    rm -rf build/
    log_success "✅ Clean complete!"
}

show_status() {
    log_info "📊 Riva Gleam Project Status"
    echo "============================="
    echo ""
    echo -e "${GREEN}Project Info:${RESET}"
    echo "  Name: $(grep '^name' gleam.toml | cut -d'"' -f2)"
    echo "  Version: $(grep '^version' gleam.toml | cut -d'"' -f2)"
    echo ""
    echo -e "${GREEN}Build Status:${RESET}"
    if [ -d "build/dev/javascript" ]; then
        echo -e "  JavaScript: ${GREEN}✅ Built${RESET}"
    else
        echo -e "  JavaScript: ${RED}❌ Not built${RESET}"
    fi
    if [ -d "build/dev/erlang" ]; then
        echo -e "  Erlang: ${GREEN}✅ Built${RESET}"
    else
        echo -e "  Erlang: ${RED}❌ Not built${RESET}"
    fi
    echo ""
    echo -e "${GREEN}Running Processes:${RESET}"
    if pgrep -f "python3 serve.py" > /dev/null; then
        echo -e "  Frontend Server: ${GREEN}✅ Running on port $PORT${RESET}"
    else
        echo -e "  Frontend Server: ${RED}❌ Not running${RESET}"
    fi
    if pgrep -f "gleam run" > /dev/null; then
        echo -e "  API Server: ${GREEN}✅ Running${RESET}"
    else
        echo -e "  API Server: ${RED}❌ Not running${RESET}"
    fi
}

stop_servers() {
    log_info "🛑 Stopping development servers..."
    pkill -f "python3 serve.py" || log_warning "No Python server running"
    pkill -f "gleam run" || log_warning "No Gleam server running"
    log_success "✅ Servers stopped!"
}

# Main command handling
case "${1:-help}" in
    "help"|"-h"|"--help")
        show_help
        ;;
    "install")
        install_deps
        ;;
    "build"|"build-js")
        build_js
        ;;
    "build-erl")
        build_erl
        ;;
    "test")
        run_tests
        ;;
    "test-watch")
        run_tests_watch
        ;;
    "dev")
        start_dev
        ;;
    "format")
        format_code
        ;;
    "check")
        check_format
        ;;
    "docs")
        generate_docs
        ;;
    "clean")
        clean_build
        ;;
    "status")
        show_status
        ;;
    "stop")
        stop_servers
        ;;
    *)
        log_error "❌ Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
